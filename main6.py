# not working
"""
Norwegian Audio Transcription Script
Real-time transcription of Norwegian audio from microphone using OpenAI's Realtime API
"""

import asyncio
import json
import base64
import pyaudio
import websockets
import os
from typing import Optional
import logging
from dotenv import load_dotenv

load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class NorwegianTranscriber:
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.websocket = None
        self.audio_stream = None
        self.p = None

        # Audio configuration
        self.CHUNK = 1024
        self.FORMAT = pyaudio.paInt16
        self.CHANNELS = 1
        self.RATE = 16000  # OpenAI Realtime API expects 24kHz

        # WebSocket URL for OpenAI Realtime API
        self.WS_URL = "wss://api.openai.com/v1/realtime?intent=transcription"

    async def connect_websocket(self):
        """Connect to OpenAI Realtime API WebSocket"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "OpenAI-Beta": "realtime=v1"
        }

        try:
            self.websocket = await websockets.connect(
                f"{self.WS_URL}",
                additional_headers=headers
            )
            logger.info("Connected to OpenAI Realtime API")

            # Configure session for Norwegian transcription
            session_config = {
                "type": "realtime.transcription_session",
                "session": {
                    # "modalities": ["text", "audio"],
                    # "instructions": "You are a helpful assistant that transcribes Norwegian audio. Always respond in Norwegian and provide accurate transcriptions.",
                    # "voice": "alloy",
                    "input_audio_format": "pcm16",
                    # "output_audio_format": "pcm16",
                    "input_audio_transcription": {
                        "model": "gpt-4o-transcribe",
                        # "language": "en"  # Norwegian language code
                    },
                    "turn_detection": {
                        "type": "server_vad",
                        "threshold": 0.5,
                        "prefix_padding_ms": 300,
                        "silence_duration_ms": 500
                    }
                }
            }

            await self.websocket.send(json.dumps(session_config))
            logger.info("Session configured for Norwegian transcription")

        except Exception as e:
            logger.error(f"Failed to connect to WebSocket: {e}")
            raise

    def setup_audio(self):
        """Initialize audio input stream"""
        self.p = pyaudio.PyAudio()

        # List available audio devices (for debugging)
        logger.info("Available audio devices:")
        for i in range(self.p.get_device_count()):
            info = self.p.get_device_info_by_index(i)
            if info['maxInputChannels'] > 0:
                logger.info(f"  {i}: {info['name']}")

        try:
            self.audio_stream = self.p.open(
                format=self.FORMAT,
                channels=self.CHANNELS,
                rate=self.RATE,
                input=True,
                frames_per_buffer=self.CHUNK
            )
            logger.info(f"Audio stream initialized: {self.RATE}Hz, {self.CHANNELS} channel(s)")
        except Exception as e:
            logger.error(f"Failed to initialize audio stream: {e}")
            raise

    async def send_audio_data(self):
        """Capture and send audio data to WebSocket"""
        try:
            while True:
                # Read audio data from microphone
                audio_data = self.audio_stream.read(self.CHUNK, exception_on_overflow=False)

                # Encode audio data as base64
                audio_base64 = base64.b64encode(audio_data).decode('utf-8')

                # Send audio data to WebSocket
                audio_message = {
                    "type": "input_audio_buffer.append",
                    "audio": audio_base64
                }

                await self.websocket.send(json.dumps(audio_message))

                # Small delay to prevent overwhelming the API
                await asyncio.sleep(0.01)

        except Exception as e:
            logger.error(f"Error sending audio data: {e}")

    async def receive_responses(self):
        """Receive and process responses from WebSocket"""
        try:
            async for message in self.websocket:
                try:
                    data = json.loads(message)
                    await self.handle_response(data)
                except json.JSONDecodeError:
                    logger.error("Failed to decode JSON response")

        except websockets.exceptions.ConnectionClosed:
            logger.info("WebSocket connection closed")
        except Exception as e:
            logger.error(f"Error receiving responses: {e}")

    async def handle_response(self, data: dict):
        """Handle different types of responses from the API"""
        response_type = data.get("type", "")

        if response_type == "session.created":
            logger.info("Session created successfully")

        elif response_type == "input_audio_buffer.speech_started":
            logger.info("🎤 Speech detected...")

        elif response_type == "input_audio_buffer.speech_stopped":
            logger.info("🔇 Speech ended")

        elif response_type == "conversation.item.input_audio_transcription.completed":
            transcript = data.get("transcript", "")
            if transcript:
                print(f"📝 Norwegian Transcription: {transcript}")

        elif response_type == "response.text.delta":
            # Handle streaming text responses
            delta = data.get("delta", "")
            if delta:
                print(f"💬 Response: {delta}", end="", flush=True)

        elif response_type == "response.text.done":
            print()  # New line after complete response

        elif response_type == "error":
            error_msg = data.get("error", {}).get("message", "Unknown error")
            logger.error(f"API Error: {error_msg}")

        # Uncomment for debugging - shows all message types
        # else:
        #     logger.debug(f"Received: {response_type}")

    async def start_transcription(self):
        """Start the transcription process"""
        try:
            # Setup audio
            self.setup_audio()

            # Connect to WebSocket
            await self.connect_websocket()

            print("🎙️  Starting Norwegian audio transcription...")
            print("🗣️  Speak in Norwegian, and the transcription will appear below.")
            print("⏹️  Press Ctrl+C to stop.\n")

            # Start both sending and receiving tasks
            send_task = asyncio.create_task(self.send_audio_data())
            receive_task = asyncio.create_task(self.receive_responses())

            # Wait for either task to complete (or be cancelled)
            await asyncio.gather(send_task, receive_task)

        except KeyboardInterrupt:
            print("\n🛑 Transcription stopped by user")
        except Exception as e:
            logger.error(f"Error in transcription: {e}")
        finally:
            await self.cleanup()

    async def cleanup(self):
        """Clean up resources"""
        if self.audio_stream:
            self.audio_stream.stop_stream()
            self.audio_stream.close()

        if self.p:
            self.p.terminate()

        if self.websocket:
            await self.websocket.close()

        logger.info("Resources cleaned up")


async def main():
    """Main function"""
    # Get OpenAI API key from environment variable
    api_key = os.getenv("OPENAI_API_KEY")

    if not api_key:
        print("❌ Error: OPENAI_API_KEY environment variable not set")
        print("Please set your OpenAI API key:")
        print("export OPENAI_API_KEY='your-api-key-here'")
        return

    # Create transcriber instance
    transcriber = NorwegianTranscriber(api_key)

    # Start transcription
    await transcriber.start_transcription()


if __name__ == "__main__":
    # Check for required packages
    try:
        import pyaudio
        import websockets
    except ImportError as e:
        print(f"❌ Missing required package: {e}")
        print("Please install required packages:")
        print("pip install pyaudio websockets")
        exit(1)

    # Run the main function
    asyncio.run(main())