# Generated with <PERSON>, records audio and transcribes it only after you press enter
import openai
import pyaudio
import wave
import tempfile
import os
import threading
from dotenv import load_dotenv

load_dotenv()

# --- Configuration ---
CHUNK = 1024
FORMAT = pyaudio.paInt16
CHANNELS = 1
RATE = 16000  # OpenAI recommends 16000 Hz for optimal performance
MODEL_ENGINE = "gpt-4o-mini-transcribe"  # Or you can use other models like gpt-4o-transcribe
LANGUAGE = "en"  # ISO-639-1 code for Norwegian

# --- Main Transcription Logic ---

def record_and_transcribe():
    """
    Records audio from the microphone and transcribes it using OpenAI's API.
    """
    audio = pyaudio.PyAudio()
    is_recording = threading.Event()
    is_recording.set()

    def record_audio(stream, frames):
        print("Recording...")
        while is_recording.is_set():
            data = stream.read(CHUNK)
            frames.append(data)
        print("Finished recording.")

    stream = audio.open(format=FORMAT,
                        channels=CHANNELS,
                        rate=RATE,
                        input=True,
                        frames_per_buffer=CHUNK)

    frames = []
    record_thread = threading.Thread(target=record_audio, args=(stream, frames))
    record_thread.start()

    input("Press Enter to stop recording...")
    is_recording.clear()
    record_thread.join()

    stream.stop_stream()
    stream.close()
    audio.terminate()

    # Save the recorded data as a temporary WAV file
    with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as temp_audio_file:
        wf = wave.open(temp_audio_file.name, 'wb')
        wf.setnchannels(CHANNELS)
        wf.setsampwidth(audio.get_sample_size(FORMAT))
        wf.setframerate(RATE)
        wf.writeframes(b''.join(frames))
        wf.close()
        temp_audio_filename = temp_audio_file.name

    try:
        # Transcribe the audio file
        client = openai.OpenAI()
        with open(temp_audio_filename, "rb") as audio_file:
            transcription = client.audio.transcriptions.create(
                model=MODEL_ENGINE,
                file=audio_file,
                language=LANGUAGE
            )
        print("\nTranscription:")
        print(transcription.text)
    except openai.APIError as e:
        print(f"An OpenAI API error occurred: {e}")
    except Exception as e:
        print(f"An error occurred: {e}")
    finally:
        # Clean up the temporary file
        if os.path.exists(temp_audio_filename):
            os.remove(temp_audio_filename)


if __name__ == "__main__":
    # Check if the API key is set
    if not os.getenv("OPENAI_API_KEY"):
        print("Error: The OPENAI_API_KEY was not found in the .env file.")
        print("Please create a .env file and add your OpenAI API key to it.")
    else:
        record_and_transcribe()